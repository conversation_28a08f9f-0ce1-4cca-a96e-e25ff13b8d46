#!/usr/bin/env python3
"""
LORE-TSR 骨干网络模块初始化文件

管理所有LORE-TSR支持的骨干网络架构

Time: 2025-07-18
Author: LORE-TSR Migration Team
Description: 骨干网络模块，包含FPN+ResNet、DLA+DCN等架构
"""

# 版本信息
__version__ = "1.0.0"

# 迭代1：基础导出接口（空实现占位）
# 后续迭代将逐步取消注释并实现

# 迭代2：骨干网络导出
# from .fpn_resnet_half import get_pose_net as get_fpn_resnet_half
# from .fpn_resnet import get_pose_net as get_fpn_resnet
# from .fpn_mask_resnet_half import get_pose_net as get_fpn_mask_resnet_half
# from .fpn_mask_resnet import get_pose_net as get_fpn_mask_resnet
# from .pose_dla_dcn import get_pose_net as get_pose_dla_dcn

# 骨干网络工厂函数
# BACKBONE_FACTORY = {
#     'resfpnhalf_18': get_fpn_resnet_half,
#     'resfpn_18': get_fpn_resnet,
#     'resfpnmaskhalf_18': get_fpn_mask_resnet_half,
#     'resfpnmask_18': get_fpn_mask_resnet,
#     'dla_34': get_pose_dla_dcn,
# }

__all__ = [
    "__version__",
    # 后续迭代将添加：
    # "BACKBONE_FACTORY",
    # "get_fpn_resnet_half",
    # "get_fpn_resnet",
    # "get_fpn_mask_resnet_half",
    # "get_fpn_mask_resnet",
    # "get_pose_dla_dcn",
]
